Create a comprehensive BookBrain - Personal Library Assistant project with the following deliverables:

**Phase 1: Project Planning & Documentation**
1. Generate a professional GitHub README.md that includes:
   - Project overview and pain points solved
   - System architecture diagram (Mermaid syntax)
   - Workflow diagram (Mermaid syntax)  
   - Complete project structure with file organization
   - Installation and setup instructions
   - Usage examples and screenshots placeholders

2. Perform domain research:
   - Check if "bookbrain" domain variations are available (.com, .io, .app)
   - Suggest 5-10 alternative domain names that are:
     - Available for purchase
     - Affordable (under $20/year)
     - Memorable and brandable
     - Related to books/learning/research themes

**Phase 2: Technical Implementation**
Create a complete codebase with this exact structure:
```
BookBrain/
├── frontend/ (React + Electron)
├── backend/ (Python FastAPI)
├── database/ (SQLite schemas)
├── memvid-integration/ (PDF processing)
└── docs/ (documentation)
```

**Core Features to Implement:**
1. **PDF Upload System**: Drag-and-drop interface with real-time progress indicators
2. **MemVid Integration**: Convert PDFs to searchable format using https://github.com/Olow304/memvid
3. **Semantic Search**: Advanced search with filters (book, date, topic, content similarity)
4. **AI Chat Interface**: Integration with free/open APIs (Ollama, Hugging Face) for book Q&A
5. **Note-taking System**: Highlight and annotate with links to specific book sections
6. **Export Features**: Generate PDF summaries, markdown notes, conversation transcripts
7. **Offline Functionality**: Local storage and processing capabilities

**Technical Requirements:**
- Frontend: React + Electron (cross-platform desktop app)
- Backend: Python FastAPI with async support
- Database: SQLite for metadata and user data
- UI: Tailwind CSS for modern, responsive design
- PDF Processing: PyPDF2/pdfplumber + MemVid integration
- AI: Use free APIs (Ollama local models, Hugging Face Transformers)
- Search: Implement vector similarity search with sentence-transformers

**Deliverables Format:**
For each file in the project structure:
1. Create the complete file content in a separate code block
2. Include the exact file path and name as header
3. Write a descriptive git commit message after each file
4. Ensure all code uses free/open-source libraries and APIs
5. Include comprehensive error handling and logging
6. Add inline documentation and comments

**GitHub Integration:**
- Target repository: https://github.com/HectorTa1989/BookBrain
- Each file should be production-ready with proper imports and dependencies
- Include package.json, requirements.txt, and other config files
- Provide clear setup instructions for development environment

Focus on creating a professional, scalable codebase that can be immediately deployed and used by students and researchers.