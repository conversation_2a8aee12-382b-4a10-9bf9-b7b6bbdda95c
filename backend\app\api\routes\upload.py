"""
BookBrain Upload API
Handles PDF and document upload with real-time processing
"""

import os
import uuid
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from loguru import logger

from core.config import settings
from core.database import get_db
from models.database import Document
from services.document_processor import DocumentProcessor
from services.memvid_service import MemvidService
from utils.file_utils import validate_file, save_upload_file, get_file_info

router = APIRouter()


@router.post("/file")
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """
    Upload a single file (PDF, TXT, MD, DOCX) for processing.
    
    Args:
        file: The uploaded file
        title: Optional custom title for the document
        db: Database session
        
    Returns:
        dict: Upload response with document ID and processing status
    """
    try:
        # Validate file
        validation_result = validate_file(file)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=validation_result["error"]
            )
        
        # Generate unique filename
        file_extension = Path(file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
        
        # Save uploaded file
        await save_upload_file(file, file_path)
        
        # Get file information
        file_info = get_file_info(file_path)
        
        # Create document record
        document = Document(
            title=title or Path(file.filename).stem,
            filename=file.filename,
            file_path=file_path,
            file_size=file_info["size"],
            file_type=file_extension,
            mime_type=file.content_type,
            processing_status="pending"
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Start background processing
        background_tasks.add_task(
            process_document_background,
            document.id,
            file_path
        )
        
        logger.info(f"📤 File uploaded successfully: {file.filename} (ID: {document.id})")
        
        return {
            "success": True,
            "document_id": document.id,
            "filename": file.filename,
            "title": document.title,
            "file_size": file_info["size"],
            "status": "uploaded",
            "message": "File uploaded successfully. Processing started in background."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Upload failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Upload failed: {str(e)}"
        )


@router.post("/multiple")
async def upload_multiple_files(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    """
    Upload multiple files for batch processing.
    
    Args:
        files: List of uploaded files
        db: Database session
        
    Returns:
        dict: Batch upload response with document IDs and statuses
    """
    try:
        if len(files) > settings.MAX_CONCURRENT_UPLOADS:
            raise HTTPException(
                status_code=400,
                detail=f"Too many files. Maximum {settings.MAX_CONCURRENT_UPLOADS} files allowed."
            )
        
        results = []
        
        for file in files:
            try:
                # Validate file
                validation_result = validate_file(file)
                if not validation_result["valid"]:
                    results.append({
                        "filename": file.filename,
                        "success": False,
                        "error": validation_result["error"]
                    })
                    continue
                
                # Generate unique filename
                file_extension = Path(file.filename).suffix.lower()
                unique_filename = f"{uuid.uuid4()}{file_extension}"
                file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
                
                # Save uploaded file
                await save_upload_file(file, file_path)
                
                # Get file information
                file_info = get_file_info(file_path)
                
                # Create document record
                document = Document(
                    title=Path(file.filename).stem,
                    filename=file.filename,
                    file_path=file_path,
                    file_size=file_info["size"],
                    file_type=file_extension,
                    mime_type=file.content_type,
                    processing_status="pending"
                )
                
                db.add(document)
                db.commit()
                db.refresh(document)
                
                # Start background processing
                background_tasks.add_task(
                    process_document_background,
                    document.id,
                    file_path
                )
                
                results.append({
                    "filename": file.filename,
                    "document_id": document.id,
                    "success": True,
                    "file_size": file_info["size"]
                })
                
            except Exception as e:
                logger.error(f"❌ Failed to upload {file.filename}: {str(e)}")
                results.append({
                    "filename": file.filename,
                    "success": False,
                    "error": str(e)
                })
        
        successful_uploads = len([r for r in results if r["success"]])
        
        logger.info(f"📤 Batch upload completed: {successful_uploads}/{len(files)} files successful")
        
        return {
            "success": True,
            "total_files": len(files),
            "successful_uploads": successful_uploads,
            "results": results,
            "message": f"Batch upload completed. {successful_uploads} files uploaded successfully."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Batch upload failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Batch upload failed: {str(e)}"
        )


@router.get("/status/{document_id}")
async def get_upload_status(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Get the processing status of an uploaded document.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        dict: Processing status and progress information
    """
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        
        if not document:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
        
        return {
            "document_id": document.id,
            "filename": document.filename,
            "title": document.title,
            "status": document.processing_status,
            "error": document.processing_error,
            "chunk_count": document.chunk_count,
            "created_at": document.created_at,
            "processed_at": document.processed_at,
            "memvid_ready": bool(document.memvid_file_path and document.memvid_index_path)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get upload status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get upload status: {str(e)}"
        )


@router.delete("/{document_id}")
async def delete_uploaded_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete an uploaded document and all associated files.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        dict: Deletion confirmation
    """
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        
        if not document:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
        
        # Delete physical files
        files_to_delete = [
            document.file_path,
            document.memvid_file_path,
            document.memvid_index_path
        ]
        
        for file_path in files_to_delete:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"🗑️ Deleted file: {file_path}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to delete file {file_path}: {e}")
        
        # Delete database record (cascades to chunks, notes, etc.)
        db.delete(document)
        db.commit()
        
        logger.info(f"🗑️ Document deleted successfully: {document.filename} (ID: {document_id})")
        
        return {
            "success": True,
            "message": f"Document '{document.filename}' deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete document: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete document: {str(e)}"
        )


async def process_document_background(document_id: int, file_path: str):
    """
    Background task to process uploaded document.
    
    Args:
        document_id: Document ID
        file_path: Path to uploaded file
    """
    from core.database import SessionLocal
    
    db = SessionLocal()
    try:
        # Get document
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            logger.error(f"❌ Document not found for processing: {document_id}")
            return
        
        # Update status to processing
        document.processing_status = "processing"
        db.commit()
        
        logger.info(f"🔄 Starting document processing: {document.filename}")
        
        # Initialize services
        doc_processor = DocumentProcessor()
        memvid_service = MemvidService()
        
        # Process document
        processing_result = await doc_processor.process_document(document, file_path)
        
        if processing_result["success"]:
            # Create MemVid representation
            memvid_result = await memvid_service.create_memory_video(
                document_id=document.id,
                chunks=processing_result["chunks"]
            )
            
            if memvid_result["success"]:
                # Update document with MemVid paths
                document.memvid_file_path = memvid_result["video_path"]
                document.memvid_index_path = memvid_result["index_path"]
                document.chunk_count = len(processing_result["chunks"])
                document.processing_status = "completed"
                document.processed_at = func.now()
                
                logger.info(f"✅ Document processing completed: {document.filename}")
            else:
                document.processing_status = "failed"
                document.processing_error = memvid_result["error"]
                logger.error(f"❌ MemVid processing failed: {memvid_result['error']}")
        else:
            document.processing_status = "failed"
            document.processing_error = processing_result["error"]
            logger.error(f"❌ Document processing failed: {processing_result['error']}")
        
        db.commit()
        
    except Exception as e:
        logger.error(f"❌ Background processing failed: {str(e)}")
        if document:
            document.processing_status = "failed"
            document.processing_error = str(e)
            db.commit()
    finally:
        db.close()
