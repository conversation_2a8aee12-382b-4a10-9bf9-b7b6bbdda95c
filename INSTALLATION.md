# BookBrain Installation Guide

This guide will help you set up Book<PERSON>rain - Personal Library Assistant on your system.

## 🎯 Quick Start

### Automated Setup (Recommended)

**Linux/macOS:**
```bash
git clone https://github.com/HectorTa1989/BookBrain.git
cd BookBrain
chmod +x scripts/setup.sh
./scripts/setup.sh
```

**Windows:**
```cmd
git clone https://github.com/HectorTa1989/BookBrain.git
cd BookBrain
scripts\setup.bat
```

### Manual Setup

If you prefer to set up manually or the automated script doesn't work:

## 📋 Prerequisites

- **Python 3.8+** with pip
- **Node.js 18+** with npm
- **Git** for version control
- **SQLite** (usually included with Python)
- **FFmpeg** (for video processing)

### Optional:
- **Docker & Docker Compose** (for production deployment)
- **Redis** (for background tasks in production)
- **Ollama** (for local AI models)

## 🔧 Manual Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/HectorTa1989/BookBrain.git
cd BookBrain
```

### 2. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env

# Create necessary directories
mkdir -p uploads memvid_storage exports logs static data

# Initialize database
python -c "from app.core.database import init_db; init_db()"

cd ..
```

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

cd ..
```

### 4. Database Setup

```bash
# Create database directory
mkdir -p database/data

# Initialize database schema (if SQLite CLI is available)
sqlite3 database/data/bookbrain.db < database/schemas/init.sql
```

## 🚀 Running BookBrain

### Development Mode

**Option 1: Using startup script**
```bash
# Linux/macOS
./start-dev.sh

# Windows
start-dev.bat
```

**Option 2: Manual startup**

Terminal 1 (Backend):
```bash
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

Terminal 2 (Frontend):
```bash
cd frontend
npm start
```

**Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Production Mode

**Using Docker Compose (Recommended):**
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

**Manual Production Setup:**
```bash
# Build frontend
cd frontend
npm run build
cd ..

# Start backend with production settings
cd backend
source venv/bin/activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## ⚙️ Configuration

### Backend Configuration (.env)

Key settings in `backend/.env`:

```env
# Application
DEBUG=true
ENVIRONMENT=development

# Server
HOST=127.0.0.1
PORT=8000

# Database
DATABASE_URL=sqlite:///./bookbrain.db

# AI Settings
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
EMBEDDING_MODEL=all-MiniLM-L6-v2

# File Upload
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_FILE_TYPES=.pdf,.txt,.md,.docx
```

### Frontend Configuration (.env)

Key settings in `frontend/.env`:

```env
# API
REACT_APP_API_URL=http://localhost:8000/api/v1

# Features
REACT_APP_MAX_FILE_SIZE=104857600
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.txt,.md,.docx
REACT_APP_DEFAULT_AI_MODEL=llama2
```

## 🤖 AI Model Setup

### Ollama (Local AI - Recommended)

1. **Install Ollama:**
   - Linux: `curl -fsSL https://ollama.ai/install.sh | sh`
   - macOS: `brew install ollama`
   - Windows: Download from https://ollama.ai

2. **Download a model:**
   ```bash
   ollama pull llama2
   # or for a smaller model:
   ollama pull llama2:7b
   ```

3. **Start Ollama service:**
   ```bash
   ollama serve
   ```

### Alternative AI APIs

You can also use cloud-based AI services by setting these in your `.env`:

```env
# OpenAI (optional)
OPENAI_API_KEY=your_openai_api_key

# Hugging Face (optional)
HUGGINGFACE_API_KEY=your_huggingface_api_key
```

## 📱 Desktop App (Electron)

To run as a desktop application:

```bash
cd frontend

# Development
npm run electron:dev

# Build for production
npm run electron:pack
```

## 🐳 Docker Deployment

### Development with Docker

```bash
# Build and run backend only
docker build -t bookbrain-backend ./backend
docker run -p 8000:8000 bookbrain-backend
```

### Production with Docker Compose

```bash
# Start all services
docker-compose up -d

# Scale workers
docker-compose up -d --scale celery-worker=3

# View logs
docker-compose logs -f backend

# Update services
docker-compose pull
docker-compose up -d
```

## 🔍 Troubleshooting

### Common Issues

**1. Python/Node.js not found**
- Ensure Python 3.8+ and Node.js 18+ are installed and in PATH

**2. Permission errors on Linux/macOS**
```bash
chmod +x scripts/setup.sh
chmod +x start-dev.sh
```

**3. Database initialization fails**
```bash
cd backend
python -c "from app.core.database import init_db; init_db()"
```

**4. Frontend build fails**
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

**5. MemVid processing errors**
- Ensure FFmpeg is installed
- Check file permissions in upload directories

### Getting Help

- **Documentation:** https://github.com/HectorTa1989/BookBrain
- **Issues:** https://github.com/HectorTa1989/BookBrain/issues
- **Discussions:** https://github.com/HectorTa1989/BookBrain/discussions

## 🔄 Updates

To update BookBrain:

```bash
git pull origin main

# Update backend dependencies
cd backend
source venv/bin/activate
pip install -r requirements.txt

# Update frontend dependencies
cd ../frontend
npm install

# Restart services
./start-dev.sh
```

## 🧪 Testing

Run tests to ensure everything is working:

```bash
# Backend tests
cd backend
pytest tests/

# Frontend tests
cd ../frontend
npm test
```

## 📊 Monitoring

### Development
- Backend logs: Check terminal output
- Frontend logs: Check browser console
- Database: Use SQLite browser tools

### Production
- Application logs: `docker-compose logs -f`
- System metrics: Use Docker stats
- Health checks: Visit `/health` endpoint

---

**Need help?** Check our [troubleshooting guide](https://github.com/HectorTa1989/BookBrain/wiki/Troubleshooting) or [open an issue](https://github.com/HectorTa1989/BookBrain/issues).
