"""
BookBrain MemVid Integration Service
Handles video-based AI memory creation and semantic search using MemVid
"""

import os
import json
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional

from loguru import logger
from memvid import MemvidEncoder, MemvidRetriever, MemvidChat
from sentence_transformers import SentenceTransformer

from core.config import settings
from models.database import Document, DocumentChunk


class MemvidService:
    """Service for integrating with MemVid library for video-based memory storage."""
    
    def __init__(self):
        """Initialize MemVid service with configuration."""
        self.embedding_model = SentenceTransformer(settings.EMBEDDING_MODEL)
        self.storage_dir = Path(settings.MEMVID_STORAGE_DIR)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # MemVid configuration
        self.memvid_config = {
            "chunk_size": settings.MEMVID_CHUNK_SIZE,
            "overlap": settings.MEMVID_OVERLAP,
            "fps": settings.MEMVID_FPS,
            "frame_size": settings.MEMVID_FRAME_SIZE,
            "video_codec": settings.MEMVID_VIDEO_CODEC,
            "crf": settings.MEMVID_CRF,
        }
        
        logger.info("🎬 MemVid service initialized")
    
    async def create_memory_video(
        self,
        document_id: int,
        chunks: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a MemVid memory video from document chunks.
        
        Args:
            document_id: Document ID
            chunks: List of text chunks with metadata
            metadata: Additional metadata for the video
            
        Returns:
            dict: Result with video path, index path, and success status
        """
        try:
            logger.info(f"🎬 Creating MemVid memory for document {document_id} with {len(chunks)} chunks")
            
            # Generate unique filenames
            video_filename = f"doc_{document_id}_{uuid.uuid4().hex[:8]}.mp4"
            index_filename = f"doc_{document_id}_{uuid.uuid4().hex[:8]}_index.json"
            
            video_path = self.storage_dir / video_filename
            index_path = self.storage_dir / index_filename
            
            # Initialize MemVid encoder
            encoder = MemvidEncoder(
                chunk_size=self.memvid_config["chunk_size"],
                embedding_model=self.embedding_model,
                n_workers=4  # Parallel processing
            )
            
            # Prepare chunks for MemVid
            text_chunks = []
            chunk_metadata = []
            
            for i, chunk in enumerate(chunks):
                text_chunks.append(chunk["content"])
                chunk_metadata.append({
                    "chunk_id": chunk.get("id", i),
                    "page_number": chunk.get("page_number"),
                    "start_char": chunk.get("start_char"),
                    "end_char": chunk.get("end_char"),
                    "document_id": document_id,
                    **chunk.get("metadata", {})
                })
            
            # Add chunks to encoder
            logger.info(f"📝 Adding {len(text_chunks)} chunks to MemVid encoder")
            encoder.add_chunks(text_chunks, metadata=chunk_metadata)
            
            # Build video with optimized settings
            logger.info(f"🎥 Building MemVid video: {video_path}")
            encoder.build_video(
                str(video_path),
                str(index_path),
                fps=self.memvid_config["fps"],
                frame_size=self.memvid_config["frame_size"],
                video_codec=self.memvid_config["video_codec"],
                crf=self.memvid_config["crf"]
            )
            
            # Verify files were created
            if not video_path.exists() or not index_path.exists():
                raise Exception("MemVid files were not created successfully")
            
            # Get file sizes for logging
            video_size = video_path.stat().st_size
            index_size = index_path.stat().st_size
            
            logger.info(
                f"✅ MemVid memory created successfully:\n"
                f"   📹 Video: {video_path} ({video_size / 1024 / 1024:.2f} MB)\n"
                f"   📊 Index: {index_path} ({index_size / 1024:.2f} KB)\n"
                f"   📦 Chunks: {len(chunks)}"
            )
            
            return {
                "success": True,
                "video_path": str(video_path),
                "index_path": str(index_path),
                "video_size": video_size,
                "index_size": index_size,
                "chunk_count": len(chunks),
                "compression_ratio": self._calculate_compression_ratio(chunks, video_size)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to create MemVid memory: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def search_memory(
        self,
        video_path: str,
        index_path: str,
        query: str,
        top_k: int = 10,
        similarity_threshold: float = None
    ) -> Dict[str, Any]:
        """
        Search MemVid memory for relevant chunks.
        
        Args:
            video_path: Path to MemVid video file
            index_path: Path to MemVid index file
            query: Search query
            top_k: Number of results to return
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            dict: Search results with chunks and metadata
        """
        try:
            if not os.path.exists(video_path) or not os.path.exists(index_path):
                raise Exception("MemVid files not found")
            
            # Initialize retriever
            retriever = MemvidRetriever(video_path, index_path)
            
            # Perform search
            logger.info(f"🔍 Searching MemVid memory: '{query}' (top_k={top_k})")
            
            results = retriever.search(
                query,
                top_k=top_k,
                similarity_threshold=similarity_threshold or settings.SIMILARITY_THRESHOLD
            )
            
            # Process results
            processed_results = []
            for result in results:
                processed_results.append({
                    "content": result.get("text", ""),
                    "similarity_score": result.get("similarity", 0.0),
                    "metadata": result.get("metadata", {}),
                    "frame_number": result.get("frame_number"),
                    "chunk_id": result.get("metadata", {}).get("chunk_id"),
                    "page_number": result.get("metadata", {}).get("page_number"),
                    "document_id": result.get("metadata", {}).get("document_id")
                })
            
            logger.info(f"✅ MemVid search completed: {len(processed_results)} results found")
            
            return {
                "success": True,
                "query": query,
                "results": processed_results,
                "total_results": len(processed_results),
                "search_time_ms": 0  # MemVid handles timing internally
            }
            
        except Exception as e:
            logger.error(f"❌ MemVid search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "results": []
            }
    
    async def chat_with_memory(
        self,
        video_path: str,
        index_path: str,
        query: str,
        chat_history: List[Dict[str, str]] = None,
        model_name: str = None
    ) -> Dict[str, Any]:
        """
        Chat with MemVid memory using AI.
        
        Args:
            video_path: Path to MemVid video file
            index_path: Path to MemVid index file
            query: User query
            chat_history: Previous chat messages
            model_name: AI model to use
            
        Returns:
            dict: AI response with sources and context
        """
        try:
            if not os.path.exists(video_path) or not os.path.exists(index_path):
                raise Exception("MemVid files not found")
            
            # Initialize chat interface
            chat = MemvidChat(
                video_path,
                index_path,
                model_name=model_name or settings.OLLAMA_MODEL
            )
            
            logger.info(f"💬 Starting MemVid chat: '{query}'")
            
            # Get AI response
            response = chat.chat(query, history=chat_history or [])
            
            # Extract sources and context
            sources = []
            if hasattr(response, 'sources') and response.sources:
                for source in response.sources:
                    sources.append({
                        "content": source.get("text", ""),
                        "page_number": source.get("metadata", {}).get("page_number"),
                        "similarity_score": source.get("similarity", 0.0),
                        "chunk_id": source.get("metadata", {}).get("chunk_id")
                    })
            
            logger.info(f"✅ MemVid chat completed with {len(sources)} sources")
            
            return {
                "success": True,
                "query": query,
                "response": response.get("response", str(response)),
                "sources": sources,
                "model_used": model_name or settings.OLLAMA_MODEL,
                "tokens_used": response.get("tokens_used", 0),
                "response_time_ms": response.get("response_time_ms", 0)
            }
            
        except Exception as e:
            logger.error(f"❌ MemVid chat failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "response": "I apologize, but I encountered an error while processing your request."
            }
    
    def _calculate_compression_ratio(self, chunks: List[Dict[str, Any]], video_size: int) -> float:
        """
        Calculate compression ratio achieved by MemVid.
        
        Args:
            chunks: Original text chunks
            video_size: Size of compressed video file
            
        Returns:
            float: Compression ratio (original_size / compressed_size)
        """
        try:
            # Calculate original text size
            original_size = sum(len(chunk["content"].encode('utf-8')) for chunk in chunks)
            
            if video_size > 0:
                return original_size / video_size
            return 0.0
            
        except Exception:
            return 0.0
    
    async def get_memory_stats(self, video_path: str, index_path: str) -> Dict[str, Any]:
        """
        Get statistics about a MemVid memory file.
        
        Args:
            video_path: Path to MemVid video file
            index_path: Path to MemVid index file
            
        Returns:
            dict: Memory statistics
        """
        try:
            if not os.path.exists(video_path) or not os.path.exists(index_path):
                raise Exception("MemVid files not found")
            
            # Get file sizes
            video_size = os.path.getsize(video_path)
            index_size = os.path.getsize(index_path)
            
            # Load index to get chunk count
            with open(index_path, 'r') as f:
                index_data = json.load(f)
            
            chunk_count = len(index_data.get("chunks", []))
            
            return {
                "video_path": video_path,
                "index_path": index_path,
                "video_size": video_size,
                "index_size": index_size,
                "total_size": video_size + index_size,
                "chunk_count": chunk_count,
                "avg_chunk_size": video_size / chunk_count if chunk_count > 0 else 0,
                "created_at": os.path.getctime(video_path)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get memory stats: {str(e)}")
            return {
                "error": str(e)
            }
    
    async def delete_memory(self, video_path: str, index_path: str) -> bool:
        """
        Delete MemVid memory files.
        
        Args:
            video_path: Path to MemVid video file
            index_path: Path to MemVid index file
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            files_deleted = 0
            
            if os.path.exists(video_path):
                os.remove(video_path)
                files_deleted += 1
                logger.info(f"🗑️ Deleted MemVid video: {video_path}")
            
            if os.path.exists(index_path):
                os.remove(index_path)
                files_deleted += 1
                logger.info(f"🗑️ Deleted MemVid index: {index_path}")
            
            return files_deleted > 0
            
        except Exception as e:
            logger.error(f"❌ Failed to delete MemVid files: {str(e)}")
            return False
