-- BookBrain Database Schema
-- SQLite initialization script for BookBrain Personal Library Assistant

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Set journal mode to WAL for better concurrency
PRAGMA journal_mode = WAL;

-- Set synchronous mode to NORMAL for better performance
PRAGMA synchronous = NORMAL;

-- Documents table - stores uploaded PDF and text documents
CREATE TABLE IF NOT EXISTS documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(500) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100),
    
    -- Content metadata
    page_count INTEGER,
    word_count INTEGER,
    character_count INTEGER,
    language VARCHAR(10) DEFAULT 'en',
    
    -- Processing status
    processing_status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    processing_error TEXT,
    
    -- MemVid integration
    memvid_file_path VARCHAR(1000),
    memvid_index_path VARCHAR(1000),
    chunk_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME
);

-- Create indexes for documents table
CREATE INDEX IF NOT EXISTS idx_document_status ON documents(processing_status);
CREATE INDEX IF NOT EXISTS idx_document_created ON documents(created_at);
CREATE INDEX IF NOT EXISTS idx_document_title ON documents(title);

-- Document chunks table - stores processed text chunks
CREATE TABLE IF NOT EXISTS document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    
    -- Chunk content
    content TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    page_number INTEGER,
    
    -- Position information
    start_char INTEGER,
    end_char INTEGER,
    
    -- MemVid frame information
    frame_number INTEGER,
    qr_code_data TEXT,
    
    -- Embedding information
    embedding_vector BLOB, -- Serialized numpy array
    embedding_model VARCHAR(100),
    
    -- Metadata (JSON)
    metadata TEXT, -- JSON string
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create indexes for document_chunks table
CREATE INDEX IF NOT EXISTS idx_chunk_document ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_chunk_frame ON document_chunks(frame_number);
CREATE INDEX IF NOT EXISTS idx_chunk_page ON document_chunks(page_number);

-- Notes table - user annotations and highlights
CREATE TABLE IF NOT EXISTS notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    
    -- Note content
    title VARCHAR(500),
    content TEXT NOT NULL,
    note_type VARCHAR(50) DEFAULT 'note', -- note, highlight, bookmark
    
    -- Position information
    page_number INTEGER,
    start_char INTEGER,
    end_char INTEGER,
    selected_text TEXT,
    
    -- Visual information
    color VARCHAR(7) DEFAULT '#ffff00', -- Hex color
    position_data TEXT, -- JSON for complex positioning
    
    -- Metadata
    tags TEXT, -- JSON array of tags
    is_private BOOLEAN DEFAULT 1,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create indexes for notes table
CREATE INDEX IF NOT EXISTS idx_note_document ON notes(document_id);
CREATE INDEX IF NOT EXISTS idx_note_type ON notes(note_type);
CREATE INDEX IF NOT EXISTS idx_note_created ON notes(created_at);

-- Search history table - tracks user searches
CREATE TABLE IF NOT EXISTS search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Search information
    query TEXT NOT NULL,
    search_type VARCHAR(50) DEFAULT 'semantic', -- semantic, keyword, ai_chat
    
    -- Results information
    result_count INTEGER DEFAULT 0,
    response_time_ms REAL,
    
    -- Context information
    document_id INTEGER,
    filters TEXT, -- JSON search filters
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL
);

-- Create indexes for search_history table
CREATE INDEX IF NOT EXISTS idx_search_query ON search_history(query);
CREATE INDEX IF NOT EXISTS idx_search_created ON search_history(created_at);
CREATE INDEX IF NOT EXISTS idx_search_type ON search_history(search_type);

-- Chat sessions table - AI conversation sessions
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Session information
    session_name VARCHAR(200),
    context_documents TEXT, -- JSON array of document IDs
    
    -- AI configuration
    ai_model VARCHAR(100) DEFAULT 'llama2',
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2000,
    
    -- Session metadata
    message_count INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for chat_sessions table
CREATE INDEX IF NOT EXISTS idx_chat_session_created ON chat_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_session_updated ON chat_sessions(updated_at);

-- Chat messages table - conversation history
CREATE TABLE IF NOT EXISTS chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    
    -- Message content
    role VARCHAR(20) NOT NULL, -- user, assistant, system
    content TEXT NOT NULL,
    
    -- Context information
    context_chunks TEXT, -- JSON array of referenced chunks
    sources TEXT, -- JSON array of source documents and pages
    
    -- AI metadata
    model_used VARCHAR(100),
    tokens_used INTEGER,
    response_time_ms REAL,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

-- Create indexes for chat_messages table
CREATE INDEX IF NOT EXISTS idx_message_session ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_message_role ON chat_messages(role);
CREATE INDEX IF NOT EXISTS idx_message_created ON chat_messages(created_at);

-- Export jobs table - tracks export operations
CREATE TABLE IF NOT EXISTS export_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Export configuration
    export_type VARCHAR(50) NOT NULL, -- pdf, markdown, json, txt
    content_type VARCHAR(50) NOT NULL, -- document, notes, chat, search_results
    
    -- Source information
    source_ids TEXT, -- JSON array of IDs
    filters TEXT, -- JSON export filters
    
    -- Job status
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    progress REAL DEFAULT 0.0, -- 0.0 to 1.0
    error_message TEXT,
    
    -- Output information
    output_file_path VARCHAR(1000),
    output_file_size INTEGER,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- Create indexes for export_jobs table
CREATE INDEX IF NOT EXISTS idx_export_status ON export_jobs(status);
CREATE INDEX IF NOT EXISTS idx_export_created ON export_jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_export_type ON export_jobs(export_type);

-- Create triggers for updated_at timestamps
CREATE TRIGGER IF NOT EXISTS update_documents_timestamp 
    AFTER UPDATE ON documents
    FOR EACH ROW
    BEGIN
        UPDATE documents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_notes_timestamp 
    AFTER UPDATE ON notes
    FOR EACH ROW
    BEGIN
        UPDATE notes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_chat_sessions_timestamp 
    AFTER UPDATE ON chat_sessions
    FOR EACH ROW
    BEGIN
        UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create trigger to update message count in chat sessions
CREATE TRIGGER IF NOT EXISTS update_chat_session_message_count
    AFTER INSERT ON chat_messages
    FOR EACH ROW
    BEGIN
        UPDATE chat_sessions 
        SET message_count = message_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.session_id;
    END;

-- Insert initial configuration data
INSERT OR IGNORE INTO chat_sessions (id, session_name, system_prompt) VALUES (
    1, 
    'Default Session',
    'You are BookBrain, an AI assistant that helps users explore and understand their personal document library. You have access to the user''s uploaded documents and can provide detailed answers based on their content. Always cite your sources and be helpful, accurate, and concise.'
);

-- Create views for common queries
CREATE VIEW IF NOT EXISTS document_stats AS
SELECT 
    d.id,
    d.title,
    d.filename,
    d.processing_status,
    d.chunk_count,
    COUNT(n.id) as note_count,
    d.created_at,
    d.processed_at
FROM documents d
LEFT JOIN notes n ON d.id = n.document_id
GROUP BY d.id;

CREATE VIEW IF NOT EXISTS recent_activity AS
SELECT 
    'document' as activity_type,
    d.title as title,
    d.created_at as timestamp,
    d.id as item_id
FROM documents d
WHERE d.created_at >= datetime('now', '-7 days')

UNION ALL

SELECT 
    'note' as activity_type,
    COALESCE(n.title, 'Note on ' || d.title) as title,
    n.created_at as timestamp,
    n.id as item_id
FROM notes n
JOIN documents d ON n.document_id = d.id
WHERE n.created_at >= datetime('now', '-7 days')

UNION ALL

SELECT 
    'search' as activity_type,
    'Search: ' || s.query as title,
    s.created_at as timestamp,
    s.id as item_id
FROM search_history s
WHERE s.created_at >= datetime('now', '-7 days')

ORDER BY timestamp DESC
LIMIT 50;
