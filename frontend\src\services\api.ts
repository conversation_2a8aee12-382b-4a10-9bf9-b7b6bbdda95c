import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Types
export interface Document {
  id: number;
  title: string;
  filename: string;
  file_size: number;
  file_type: string;
  page_count?: number;
  word_count?: number;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  processing_error?: string;
  chunk_count: number;
  created_at: string;
  updated_at?: string;
  processed_at?: string;
}

export interface SearchResult {
  content: string;
  similarity_score: number;
  metadata: Record<string, any>;
  page_number?: number;
  document_id: number;
  document_title: string;
}

export interface ChatMessage {
  id: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sources?: SearchResult[];
  created_at: string;
}

export interface ChatSession {
  id: number;
  session_name?: string;
  message_count: number;
  created_at: string;
  updated_at: string;
}

export interface Note {
  id: number;
  document_id: number;
  title?: string;
  content: string;
  note_type: 'note' | 'highlight' | 'bookmark';
  page_number?: number;
  selected_text?: string;
  color: string;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

// Upload API
export const uploadFile = async ({ file, fileId }: { file: File; fileId: string }) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await api.post('/upload/file', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

export const uploadMultipleFiles = async (files: File[]) => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });
  
  const response = await api.post('/upload/multiple', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

export const getUploadStatus = async (documentId: number) => {
  const response = await api.get(`/upload/status/${documentId}`);
  return response.data;
};

// Documents API
export const getDocuments = async (): Promise<Document[]> => {
  const response = await api.get('/documents');
  return response.data;
};

export const getDocument = async (id: number): Promise<Document> => {
  const response = await api.get(`/documents/${id}`);
  return response.data;
};

export const deleteDocument = async (id: number) => {
  const response = await api.delete(`/documents/${id}`);
  return response.data;
};

export const updateDocument = async (id: number, data: Partial<Document>) => {
  const response = await api.put(`/documents/${id}`, data);
  return response.data;
};

// Search API
export const searchDocuments = async (params: {
  query: string;
  document_ids?: number[];
  top_k?: number;
  similarity_threshold?: number;
  search_type?: 'semantic' | 'keyword';
}): Promise<SearchResult[]> => {
  const response = await api.post('/search', params);
  return response.data.results;
};

export const getSearchHistory = async () => {
  const response = await api.get('/search/history');
  return response.data;
};

// Chat API
export const getChatSessions = async (): Promise<ChatSession[]> => {
  const response = await api.get('/chat/sessions');
  return response.data;
};

export const createChatSession = async (data: {
  session_name?: string;
  context_documents?: number[];
}) => {
  const response = await api.post('/chat/sessions', data);
  return response.data;
};

export const getChatMessages = async (sessionId: number): Promise<ChatMessage[]> => {
  const response = await api.get(`/chat/sessions/${sessionId}/messages`);
  return response.data;
};

export const sendChatMessage = async (sessionId: number, message: string) => {
  const response = await api.post(`/chat/sessions/${sessionId}/messages`, {
    message,
  });
  return response.data;
};

export const deleteChatSession = async (sessionId: number) => {
  const response = await api.delete(`/chat/sessions/${sessionId}`);
  return response.data;
};

// Notes API
export const getNotes = async (documentId?: number): Promise<Note[]> => {
  const url = documentId ? `/notes?document_id=${documentId}` : '/notes';
  const response = await api.get(url);
  return response.data;
};

export const createNote = async (data: {
  document_id: number;
  title?: string;
  content: string;
  note_type?: 'note' | 'highlight' | 'bookmark';
  page_number?: number;
  selected_text?: string;
  color?: string;
  tags?: string[];
}) => {
  const response = await api.post('/notes', data);
  return response.data;
};

export const updateNote = async (id: number, data: Partial<Note>) => {
  const response = await api.put(`/notes/${id}`, data);
  return response.data;
};

export const deleteNote = async (id: number) => {
  const response = await api.delete(`/notes/${id}`);
  return response.data;
};

// Export API
export const exportData = async (params: {
  export_type: 'pdf' | 'markdown' | 'json' | 'txt';
  content_type: 'document' | 'notes' | 'chat' | 'search_results';
  source_ids: number[];
  filters?: Record<string, any>;
}) => {
  const response = await api.post('/export', params);
  return response.data;
};

export const getExportStatus = async (jobId: number) => {
  const response = await api.get(`/export/status/${jobId}`);
  return response.data;
};

export const downloadExport = async (jobId: number) => {
  const response = await api.get(`/export/download/${jobId}`, {
    responseType: 'blob',
  });
  return response.data;
};

// Health check
export const healthCheck = async () => {
  const response = await api.get('/health');
  return response.data;
};

// Statistics
export const getStats = async () => {
  const response = await api.get('/stats');
  return response.data;
};

export default api;
