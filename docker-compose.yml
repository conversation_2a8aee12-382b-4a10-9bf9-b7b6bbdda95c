version: '3.8'

services:
  # BookBrain Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bookbrain-backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - ENVIRONMENT=production
      - DATABASE_URL=sqlite:///./data/bookbrain.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend/data:/app/data
      - ./backend/uploads:/app/uploads
      - ./backend/memvid_storage:/app/memvid_storage
      - ./backend/exports:/app/exports
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    container_name: bookbrain-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bookbrain-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DEBUG=false
      - ENVIRONMENT=production
      - DATABASE_URL=sqlite:///./data/bookbrain.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend/data:/app/data
      - ./backend/uploads:/app/uploads
      - ./backend/memvid_storage:/app/memvid_storage
      - ./backend/exports:/app/exports
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bookbrain-celery-beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DEBUG=false
      - ENVIRONMENT=production
      - DATABASE_URL=sqlite:///./data/bookbrain.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend/data:/app/data
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  # Ollama for local AI models (optional)
  ollama:
    image: ollama/ollama:latest
    container_name: bookbrain-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    environment:
      - OLLAMA_ORIGINS=http://localhost:8000,http://backend:8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: bookbrain-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis_data:
    driver: local
  ollama_data:
    driver: local

networks:
  default:
    name: bookbrain-network
