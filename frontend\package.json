{"name": "bookbrain-frontend", "version": "1.0.0", "description": "BookBrain - Personal Library Assistant <PERSON><PERSON>", "main": "electron/main.js", "homepage": "./", "private": true, "author": {"name": "BookBrain Team", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron:dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "lint": "eslint src/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src/ --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "typescript": "^5.2.2", "tailwindcss": "^3.3.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "axios": "^1.6.2", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "react-dropzone": "^14.2.3", "react-pdf": "^7.5.1", "pdfjs-dist": "^3.11.174", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "framer-motion": "^10.16.5", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "zustand": "^4.4.6", "immer": "^10.0.3", "date-fns": "^2.30.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "react-highlight-words": "^0.20.0", "react-virtualized": "^9.22.5", "@types/react-virtualized": "^9.21.29", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-split-pane": "^0.1.92", "@types/react-split-pane": "^0.1.71", "electron-store": "^8.1.0", "electron-updater": "^6.1.7"}, "devDependencies": {"electron": "^27.1.3", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "jest": "^27.5.1", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "cross-env": "^7.0.3", "dotenv": "^16.3.1"}, "build": {"appId": "com.bookbrain.app", "productName": "BookBrain", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}}