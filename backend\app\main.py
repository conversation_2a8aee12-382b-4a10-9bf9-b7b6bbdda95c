"""
BookBrain - Personal Library Assistant
Main FastAPI application entry point
"""

import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from api.routes import api_router
from core.config import settings
from core.database import init_db
from core.exceptions import setup_exception_handlers
from core.logging_config import setup_logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("🚀 Starting BookBrain API server...")
    
    # Setup logging
    setup_logging()
    
    # Initialize database
    try:
        await init_db()
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize database: {e}")
        raise
    
    # Create necessary directories
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
    os.makedirs(settings.MEMVID_STORAGE_DIR, exist_ok=True)
    os.makedirs(settings.EXPORT_DIR, exist_ok=True)
    
    logger.info("📁 Created necessary directories")
    logger.info("🎉 BookBrain API server started successfully!")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down BookBrain API server...")
    logger.info("👋 BookBrain API server stopped")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="BookBrain API",
        description="Personal Library Assistant - Transform your PDF library into an intelligent, searchable knowledge base",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Setup exception handlers
    setup_exception_handlers(app)
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")
    
    # Serve static files (for uploaded files, exports, etc.)
    if os.path.exists(settings.STATIC_DIR):
        app.mount("/static", StaticFiles(directory=settings.STATIC_DIR), name="static")
    
    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "message": "Welcome to BookBrain API",
            "version": "1.0.0",
            "description": "Personal Library Assistant - Transform your PDF library into an intelligent, searchable knowledge base",
            "docs": "/docs" if settings.DEBUG else "Documentation disabled in production",
            "status": "healthy"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT
        }
    
    return app


# Create the FastAPI app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    logger.info("🔧 Starting BookBrain in development mode...")
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning",
    )
