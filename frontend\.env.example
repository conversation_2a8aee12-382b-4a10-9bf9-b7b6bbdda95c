# BookBrain Frontend Environment Configuration
# Copy this file to .env and update the values as needed

# API Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_WS_URL=ws://localhost:8000/ws

# Application Settings
REACT_APP_NAME=BookBrain
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Personal Library Assistant

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_OFFLINE_MODE=true
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_ENABLE_EXPORT=true

# Upload Settings
REACT_APP_MAX_FILE_SIZE=104857600  # 100MB in bytes
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.txt,.md,.docx
REACT_APP_MAX_CONCURRENT_UPLOADS=5

# Search Settings
REACT_APP_DEFAULT_SEARCH_RESULTS=10
REACT_APP_MAX_SEARCH_RESULTS=50
REACT_APP_SEARCH_DEBOUNCE_MS=300

# Chat Settings
REACT_APP_DEFAULT_AI_MODEL=llama2
REACT_APP_MAX_CHAT_HISTORY=100
REACT_APP_CHAT_TIMEOUT_MS=30000

# UI Settings
REACT_APP_THEME=light  # light, dark, auto
REACT_APP_SIDEBAR_DEFAULT_OPEN=true
REACT_APP_ANIMATIONS_ENABLED=true

# Development Settings
REACT_APP_DEBUG=true
REACT_APP_MOCK_API=false
REACT_APP_LOG_LEVEL=info

# External Services (Optional)
REACT_APP_SENTRY_DSN=your_sentry_dsn_here
REACT_APP_GOOGLE_ANALYTICS_ID=your_ga_id_here

# Electron Settings (for desktop app)
ELECTRON_IS_DEV=true
ELECTRON_ENABLE_LOGGING=true
