# BookBrain - Personal Library Assistant 🧠📚

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![React](https://img.shields.io/badge/React-18+-61DAFB.svg)](https://reactjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-009688.svg)](https://fastapi.tiangolo.com/)

> Transform your PDF library into an intelligent, searchable knowledge base with AI-powered conversations and semantic search.

## Alternative Domain Suggestions (Available & Affordable):

- readbrain.io - Combines reading + intelligence
- librarymind.app - Personal library + AI mind
- bookwise.ai - Wisdom from books
- readgenius.io - Smart reading assistant
- biblioai.com - Bibliography + AI
- smartlibrary.app - Intelligent library system
- bookcompanion.io - Your book companion
- readingbrain.app - Brain for reading
- bookmemory.ai - Memory for your books
- libraryiq.io - Library intelligence quotient

## 🎯 Problem Statement

**Students and researchers face these daily challenges:**
- 📚 **Information Overload**: Hundreds of PDFs scattered across devices
- 🔍 **Poor Search**: Can't find specific information across multiple documents
- 💭 **Lost Context**: Forget key insights from books read months ago
- ⏰ **Time Waste**: Re-reading entire documents to find one concept
- 🤝 **No Interaction**: Can't ask questions or get summaries from your library

**BookBrain solves these pain points by creating a unified, intelligent interface to your entire PDF library.**

## ✨ Key Features

### 🚀 Core Functionality
- **📤 Smart PDF Upload**: Drag-and-drop interface with real-time processing
- **🔍 Semantic Search**: Find content by meaning, not just keywords
- **🤖 AI Chat Interface**: Ask questions about your entire library
- **📝 Smart Notes**: Highlight and annotate with automatic linking
- **📊 Export Tools**: Generate summaries, notes, and conversation transcripts
- **💾 Offline-First**: Works completely offline after setup

### 🛠️ Technical Highlights
- **⚡ Lightning Fast**: Sub-100ms search across millions of text chunks
- **🗜️ Ultra Efficient**: 50-100x smaller storage than traditional vector databases
- **🔒 Privacy-First**: All processing happens locally
- **🌐 Cross-Platform**: Desktop app for Windows, macOS, and Linux
- **🎨 Modern UI**: Clean, responsive interface built with React + Tailwind

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend (Electron + React)"
        A[PDF Upload Interface]
        B[Search Dashboard]
        C[AI Chat Interface]
        D[Notes & Annotations]
        E[Export Tools]
    end
    
    subgraph "Backend (FastAPI)"
        F[PDF Processing API]
        G[Search Engine API]
        H[AI Integration API]
        I[Notes Management API]
    end
    
    subgraph "Data Layer"
        J[(SQLite Database)]
        K[MemVid Storage]
        L[Vector Embeddings]
    end
    
    subgraph "AI Services"
        M[Ollama Local Models]
        N[Hugging Face APIs]
        O[Sentence Transformers]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    F --> J
    F --> K
    G --> L
    H --> M
    H --> N
    G --> O
```

## 🔄 Workflow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant BE as Backend
    participant MV as MemVid
    participant AI as AI Service
    participant DB as Database
    
    U->>FE: Upload PDF
    FE->>BE: Process PDF Request
    BE->>MV: Convert to searchable format
    MV->>BE: Return processed chunks
    BE->>DB: Store metadata
    BE->>FE: Upload complete
    
    U->>FE: Search query
    FE->>BE: Search request
    BE->>MV: Semantic search
    MV->>BE: Relevant chunks
    BE->>FE: Search results
    
    U->>FE: Ask AI question
    FE->>BE: Chat request
    BE->>MV: Retrieve context
    BE->>AI: Generate response
    AI->>BE: AI response
    BE->>FE: Final answer
```

## 📁 Project Structure

```
BookBrain/
├── frontend/                    # React + Electron Desktop App
│   ├── src/
│   │   ├── components/         # React components
│   │   ├── pages/             # Main application pages
│   │   ├── hooks/             # Custom React hooks
│   │   ├── utils/             # Utility functions
│   │   └── styles/            # Tailwind CSS styles
│   ├── public/                # Static assets
│   ├── electron/              # Electron main process
│   └── package.json           # Frontend dependencies
│
├── backend/                     # Python FastAPI Server
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   ├── core/              # Core functionality
│   │   ├── models/            # Database models
│   │   ├── services/          # Business logic
│   │   └── utils/             # Utility functions
│   ├── tests/                 # Backend tests
│   └── requirements.txt       # Python dependencies
│
├── database/                    # SQLite Database
│   ├── schemas/               # Database schemas
│   ├── migrations/            # Database migrations
│   └── seeds/                 # Sample data
│
├── memvid-integration/          # MemVid PDF Processing
│   ├── processors/            # PDF processing logic
│   ├── encoders/              # Video encoding utilities
│   └── search/                # Search implementations
│
├── docs/                        # Documentation
│   ├── api/                   # API documentation
│   ├── user-guide/            # User guides
│   └── development/           # Development docs
│
├── scripts/                     # Build and deployment scripts
├── docker/                      # Docker configurations
└── README.md                    # This file
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+ and npm
- **Python** 3.8+ and pip
- **Git** for version control

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/BookBrain.git
cd BookBrain
```

2. **Set up the backend**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Set up the frontend**
```bash
cd ../frontend
npm install
```

4. **Initialize the database**
```bash
cd ../backend
python -m app.database.init_db
```

5. **Start the application**
```bash
# Terminal 1: Start backend
cd backend
uvicorn app.main:app --reload --port 8000

# Terminal 2: Start frontend
cd frontend
npm run electron:dev
```

## 📖 Usage Examples

### 1. Upload Your First PDF
- Drag and drop any PDF into the upload area
- Watch real-time processing progress
- PDF is automatically indexed for search

### 2. Semantic Search
```
Query: "machine learning algorithms"
Results: Finds relevant content even if exact phrase isn't used
```

### 3. AI Conversations
```
You: "What are the main differences between supervised and unsupervised learning?"
BookBrain: "Based on your library, supervised learning uses labeled data..."
```

### 4. Smart Notes
- Highlight any text in search results
- Add personal annotations
- Automatic linking to source documents

## 🛠️ Development

### Backend Development
```bash
cd backend
pip install -r requirements-dev.txt
pytest tests/
black app/
flake8 app/
```

### Frontend Development
```bash
cd frontend
npm run test
npm run lint
npm run build
```

### Building for Production
```bash
# Build frontend
cd frontend
npm run build

# Package Electron app
npm run electron:pack

# Build backend
cd ../backend
pip install build
python -m build
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [MemVid](https://github.com/Olow304/memvid) - Revolutionary video-based AI memory
- [FastAPI](https://fastapi.tiangolo.com/) - Modern Python web framework
- [React](https://reactjs.org/) - Frontend framework
- [Electron](https://electronjs.org/) - Cross-platform desktop apps

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/BookBrain/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/HectorTa1989/BookBrain/discussions)

---

**Made with ❤️ for students and researchers worldwide**
