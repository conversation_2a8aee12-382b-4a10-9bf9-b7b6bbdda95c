@echo off
setlocal enabledelayedexpansion

REM BookBrain Setup Script for Windows
REM This script sets up the development environment for BookBrain

echo 🧠 BookBrain - Personal Library Assistant Setup
echo ==============================================
echo.

REM Check for Python
echo [INFO] Checking for Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python 3.8+ is required but not found
    echo Please install Python from https://python.org
    pause
    exit /b 1
)
echo [SUCCESS] Python found

REM Check for Node.js
echo [INFO] Checking for Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js 18+ is required but not found
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)
echo [SUCCESS] Node.js found

REM Check for Git
echo [INFO] Checking for Git...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Git is required but not found
    echo Please install Git from https://git-scm.com
    pause
    exit /b 1
)
echo [SUCCESS] Git found

REM Setup backend
echo.
echo [INFO] Setting up backend...
cd backend

REM Create virtual environment
echo [INFO] Creating Python virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo [INFO] Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Python dependencies
    pause
    exit /b 1
)

REM Copy environment file
if not exist .env (
    echo [INFO] Creating environment file...
    copy .env.example .env
    echo [WARNING] Please update .env file with your configuration
)

REM Create necessary directories
echo [INFO] Creating directories...
if not exist uploads mkdir uploads
if not exist memvid_storage mkdir memvid_storage
if not exist exports mkdir exports
if not exist logs mkdir logs
if not exist static mkdir static
if not exist data mkdir data

REM Initialize database
echo [INFO] Initializing database...
python -c "from app.core.database import init_db; init_db()"

cd ..
echo [SUCCESS] Backend setup completed

REM Setup frontend
echo.
echo [INFO] Setting up frontend...
cd frontend

REM Install dependencies
echo [INFO] Installing Node.js dependencies...
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Copy environment file
if not exist .env (
    echo [INFO] Creating environment file...
    copy .env.example .env
    echo [WARNING] Please update .env file with your configuration
)

cd ..
echo [SUCCESS] Frontend setup completed

REM Setup database
echo.
echo [INFO] Setting up database...
if not exist database\data mkdir database\data

REM Initialize database with schema
if exist database\schemas\init.sql (
    echo [INFO] Initializing database schema...
    sqlite3 database\data\bookbrain.db < database\schemas\init.sql
    echo [SUCCESS] Database schema initialized
)

REM Create startup scripts
echo.
echo [INFO] Creating startup scripts...

REM Development startup script
echo @echo off > start-dev.bat
echo echo 🚀 Starting BookBrain in development mode... >> start-dev.bat
echo echo. >> start-dev.bat
echo echo Starting backend... >> start-dev.bat
echo cd backend >> start-dev.bat
echo call venv\Scripts\activate.bat >> start-dev.bat
echo start /b uvicorn app.main:app --reload --host 127.0.0.1 --port 8000 >> start-dev.bat
echo cd .. >> start-dev.bat
echo echo Starting frontend... >> start-dev.bat
echo cd frontend >> start-dev.bat
echo start /b npm start >> start-dev.bat
echo cd .. >> start-dev.bat
echo echo. >> start-dev.bat
echo echo ✅ BookBrain is starting... >> start-dev.bat
echo echo 📖 Frontend: http://localhost:3000 >> start-dev.bat
echo echo 🔧 Backend API: http://localhost:8000 >> start-dev.bat
echo echo 📚 API Docs: http://localhost:8000/docs >> start-dev.bat
echo echo. >> start-dev.bat
echo echo Press any key to stop services... >> start-dev.bat
echo pause >> start-dev.bat

REM Production startup script
echo @echo off > start-prod.bat
echo echo 🚀 Starting BookBrain in production mode... >> start-prod.bat
echo echo. >> start-prod.bat
echo echo Building frontend... >> start-prod.bat
echo cd frontend >> start-prod.bat
echo npm run build >> start-prod.bat
echo cd .. >> start-prod.bat
echo echo Starting services with Docker Compose... >> start-prod.bat
echo docker-compose up -d >> start-prod.bat
echo echo ✅ BookBrain is running in production mode >> start-prod.bat
echo echo 🌐 Application: http://localhost >> start-prod.bat
echo echo 🔧 API: http://localhost/api >> start-prod.bat

echo [SUCCESS] Startup scripts created

REM Ask about Ollama installation
echo.
set /p install_ollama="Would you like to install Ollama for local AI models? (y/n): "
if /i "%install_ollama%"=="y" (
    echo [INFO] Please install Ollama manually from https://ollama.ai
    echo [INFO] After installation, run: ollama pull llama2
)

echo.
echo [SUCCESS] 🎉 BookBrain setup completed successfully!
echo.
echo Next steps:
echo 1. Update configuration files (.env) in backend\ and frontend\
echo 2. Start development server: start-dev.bat
echo 3. Open http://localhost:3000 in your browser
echo.
echo For production deployment:
echo 1. Run: start-prod.bat
echo 2. Open http://localhost in your browser
echo.
echo Documentation: https://github.com/HectorTa1989/BookBrain
echo Issues: https://github.com/HectorTa1989/BookBrain/issues
echo.
pause
