"""
BookBrain Database Models
SQLAlchemy models for the BookBrain application
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Float, Boolean, 
    ForeignKey, JSON, LargeBinary, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

Base = declarative_base()


class Document(Base):
    """Document model for storing PDF and text documents."""
    
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False, index=True)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(1000), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    mime_type = Column(String(100))
    
    # Content metadata
    page_count = Column(Integer)
    word_count = Column(Integer)
    character_count = Column(Integer)
    language = Column(String(10), default="en")
    
    # Processing status
    processing_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    processing_error = Column(Text)
    
    # MemVid integration
    memvid_file_path = Column(String(1000))
    memvid_index_path = Column(String(1000))
    chunk_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    processed_at = Column(DateTime(timezone=True))
    
    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    notes = relationship("Note", back_populates="document", cascade="all, delete-orphan")
    searches = relationship("SearchHistory", back_populates="document")
    
    # Indexes
    __table_args__ = (
        Index('idx_document_status', 'processing_status'),
        Index('idx_document_created', 'created_at'),
        Index('idx_document_title', 'title'),
    )


class DocumentChunk(Base):
    """Document chunk model for storing processed text chunks."""
    
    __tablename__ = "document_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    
    # Chunk content
    content = Column(Text, nullable=False)
    chunk_index = Column(Integer, nullable=False)
    page_number = Column(Integer)
    
    # Position information
    start_char = Column(Integer)
    end_char = Column(Integer)
    
    # MemVid frame information
    frame_number = Column(Integer)
    qr_code_data = Column(Text)
    
    # Embedding information
    embedding_vector = Column(LargeBinary)  # Serialized numpy array
    embedding_model = Column(String(100))
    
    # Metadata
    metadata = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    
    # Indexes
    __table_args__ = (
        Index('idx_chunk_document', 'document_id'),
        Index('idx_chunk_frame', 'frame_number'),
        Index('idx_chunk_page', 'page_number'),
    )


class Note(Base):
    """Note model for user annotations and highlights."""
    
    __tablename__ = "notes"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    
    # Note content
    title = Column(String(500))
    content = Column(Text, nullable=False)
    note_type = Column(String(50), default="note")  # note, highlight, bookmark
    
    # Position information
    page_number = Column(Integer)
    start_char = Column(Integer)
    end_char = Column(Integer)
    selected_text = Column(Text)
    
    # Visual information
    color = Column(String(7), default="#ffff00")  # Hex color
    position_data = Column(JSON)  # For complex positioning
    
    # Metadata
    tags = Column(JSON)  # List of tags
    is_private = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="notes")
    
    # Indexes
    __table_args__ = (
        Index('idx_note_document', 'document_id'),
        Index('idx_note_type', 'note_type'),
        Index('idx_note_created', 'created_at'),
    )


class SearchHistory(Base):
    """Search history model for tracking user searches."""
    
    __tablename__ = "search_history"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Search information
    query = Column(Text, nullable=False)
    search_type = Column(String(50), default="semantic")  # semantic, keyword, ai_chat
    
    # Results information
    result_count = Column(Integer, default=0)
    response_time_ms = Column(Float)
    
    # Context information
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=True)
    filters = Column(JSON)  # Search filters applied
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="searches")
    
    # Indexes
    __table_args__ = (
        Index('idx_search_query', 'query'),
        Index('idx_search_created', 'created_at'),
        Index('idx_search_type', 'search_type'),
    )


class ChatSession(Base):
    """Chat session model for AI conversations."""
    
    __tablename__ = "chat_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Session information
    session_name = Column(String(200))
    context_documents = Column(JSON)  # List of document IDs
    
    # AI configuration
    ai_model = Column(String(100), default="llama2")
    system_prompt = Column(Text)
    temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2000)
    
    # Session metadata
    message_count = Column(Integer, default=0)
    total_tokens_used = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_chat_session_created', 'created_at'),
        Index('idx_chat_session_updated', 'updated_at'),
    )


class ChatMessage(Base):
    """Chat message model for storing conversation history."""
    
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    
    # Message content
    role = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    
    # Context information
    context_chunks = Column(JSON)  # Referenced document chunks
    sources = Column(JSON)  # Source documents and pages
    
    # AI metadata
    model_used = Column(String(100))
    tokens_used = Column(Integer)
    response_time_ms = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")
    
    # Indexes
    __table_args__ = (
        Index('idx_message_session', 'session_id'),
        Index('idx_message_role', 'role'),
        Index('idx_message_created', 'created_at'),
    )


class ExportJob(Base):
    """Export job model for tracking export operations."""
    
    __tablename__ = "export_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Export configuration
    export_type = Column(String(50), nullable=False)  # pdf, markdown, json, txt
    content_type = Column(String(50), nullable=False)  # document, notes, chat, search_results
    
    # Source information
    source_ids = Column(JSON)  # Document IDs, chat session IDs, etc.
    filters = Column(JSON)  # Export filters
    
    # Job status
    status = Column(String(50), default="pending")  # pending, processing, completed, failed
    progress = Column(Float, default=0.0)  # 0.0 to 1.0
    error_message = Column(Text)
    
    # Output information
    output_file_path = Column(String(1000))
    output_file_size = Column(Integer)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Indexes
    __table_args__ = (
        Index('idx_export_status', 'status'),
        Index('idx_export_created', 'created_at'),
        Index('idx_export_type', 'export_type'),
    )
