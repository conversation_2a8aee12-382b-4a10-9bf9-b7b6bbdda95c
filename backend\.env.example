# BookBrain Backend Environment Configuration
# Copy this file to .env and update the values as needed

# Application Settings
APP_NAME=BookBrain
VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# Server Settings
HOST=127.0.0.1
PORT=8000
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Database Settings
DATABASE_URL=sqlite:///./bookbrain.db
DATABASE_ECHO=false

# File Storage Settings (relative to backend directory)
UPLOAD_DIR=./uploads
MEMVID_STORAGE_DIR=./memvid_storage
EXPORT_DIR=./exports
STATIC_DIR=./static

# File Upload Limits
MAX_FILE_SIZE=104857600  # 100MB in bytes
ALLOWED_FILE_TYPES=.pdf,.txt,.md,.docx

# MemVid Settings
MEMVID_CHUNK_SIZE=512
MEMVID_OVERLAP=50
MEMVID_FPS=30
MEMVID_FRAME_SIZE=256
MEMVID_VIDEO_CODEC=h264
MEMVID_CRF=23

# AI and ML Settings
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384
MAX_SEARCH_RESULTS=20
SIMILARITY_THRESHOLD=0.7

# AI API Settings
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # Optional

# Search Settings
SEARCH_INDEX_UPDATE_INTERVAL=300  # 5 minutes
VECTOR_STORE_TYPE=faiss  # Options: faiss, chromadb

# Background Task Settings (Redis required for production)
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Logging Settings
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=30 days

# Security Settings
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Performance Settings
WORKER_PROCESSES=1
MAX_CONCURRENT_UPLOADS=5
CACHE_TTL=3600  # 1 hour

# Export Settings
EXPORT_FORMATS=pdf,markdown,json,txt
MAX_EXPORT_SIZE=52428800  # 50MB in bytes
