# Core FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
sqlite3  # Built-in Python module

# PDF processing and text extraction
PyPDF2==3.0.1
pdfplumber==0.10.3
python-magic==0.4.27
python-magic-bin==0.4.14  # Windows compatibility

# MemVid integration and video processing
memvid==0.1.3
opencv-python==********
qrcode[pil]==7.4.2

# AI and ML dependencies
sentence-transformers==2.2.2
transformers==4.35.2
torch==2.1.1
numpy==1.24.4
scikit-learn==1.3.2

# Vector search and embeddings
faiss-cpu==1.7.4
chromadb==0.4.18

# HTTP client for AI APIs
httpx==0.25.2
aiohttp==3.9.1

# File handling and utilities
aiofiles==23.2.1
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2  # For testing FastAPI

# Development dependencies
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# Security and CORS
python-cors==1.0.1
cryptography==41.0.8

# Background tasks
celery==5.3.4
redis==5.0.1

# File upload and storage
boto3==1.34.0  # Optional: for cloud storage
pillow==10.1.0

# Progress tracking
tqdm==4.66.1

# Configuration management
pyyaml==6.0.1
toml==0.10.2

# Date and time utilities
python-dateutil==2.8.2
pytz==2023.3

# Async utilities
asyncio-mqtt==0.16.1  # Optional: for real-time updates

# Memory optimization
psutil==5.9.6

# Text processing
nltk==3.8.1
spacy==3.7.2

# Export functionality
reportlab==4.0.7  # PDF generation
markdown==3.5.1
jinja2==3.1.2

# API documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
