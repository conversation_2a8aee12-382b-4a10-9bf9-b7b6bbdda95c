@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  *:focus {
    outline: none;
  }

  *:focus-visible {
    @apply ring-2 ring-blue-500 ring-offset-2;
  }

  /* Selection styles */
  ::selection {
    @apply bg-blue-100 text-blue-900;
  }

  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-xl lg:text-2xl;
  }

  h4 {
    @apply text-lg lg:text-xl;
  }

  h5 {
    @apply text-base lg:text-lg;
  }

  h6 {
    @apply text-sm lg:text-base;
  }

  /* Links */
  a {
    @apply text-blue-600 hover:text-blue-700 transition-colors duration-150;
  }

  /* Code blocks */
  code {
    @apply font-mono text-sm bg-gray-100 px-1 py-0.5 rounded;
  }

  pre {
    @apply font-mono text-sm bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
  }

  pre code {
    @apply bg-transparent p-0;
  }

  /* Form elements */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  textarea,
  select {
    @apply border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500;
  }

  /* Buttons */
  button {
    @apply transition-all duration-150 ease-in-out;
  }

  /* Tables */
  table {
    @apply w-full border-collapse;
  }

  th {
    @apply text-left font-semibold text-gray-900 border-b border-gray-200 pb-2;
  }

  td {
    @apply border-b border-gray-100 py-2;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-150;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-warning {
    @apply btn bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-error {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-outline {
    @apply btn bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  .btn-ghost {
    @apply btn bg-transparent border-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 shadow-none;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }

  .input-error {
    @apply border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-blue-100 text-blue-800;
  }

  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }

  /* Tooltip */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
  }

  .tooltip.show {
    @apply opacity-100;
  }

  /* Modal backdrop */
  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300;
  }

  /* Dropdown */
  .dropdown {
    @apply absolute z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer;
  }

  /* Progress bar */
  .progress {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-bar {
    @apply bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out;
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Highlight text */
  .highlight {
    @apply bg-yellow-200 px-1 py-0.5 rounded;
  }

  /* Divider */
  .divider {
    @apply border-t border-gray-200 my-4;
  }

  /* Status indicators */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full;
  }

  .status-online {
    @apply status-dot bg-green-400;
  }

  .status-offline {
    @apply status-dot bg-gray-400;
  }

  .status-busy {
    @apply status-dot bg-red-400;
  }

  .status-away {
    @apply status-dot bg-yellow-400;
  }
}

/* Utility styles */
@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Interaction utilities */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }

  /* Layout utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-gray-100;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    @apply text-black bg-white;
  }

  .card {
    @apply shadow-none border border-gray-300;
  }
}
