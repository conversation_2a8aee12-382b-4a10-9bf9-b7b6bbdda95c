#!/bin/bash

# BookBrain Setup Script
# This script sets up the development environment for BookBrain

set -e  # Exit on any error

echo "🧠 BookBrain - Personal Library Assistant Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        print_success "Windows detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check for required tools
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python $PYTHON_VERSION found"
    else
        print_error "Python 3.8+ is required but not found"
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js $NODE_VERSION found"
    else
        print_error "Node.js 18+ is required but not found"
        exit 1
    fi
    
    # Check npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm $NPM_VERSION found"
    else
        print_error "npm is required but not found"
        exit 1
    fi
    
    # Check Git
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version | cut -d' ' -f3)
        print_success "Git $GIT_VERSION found"
    else
        print_error "Git is required but not found"
        exit 1
    fi
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
    
    # Activate virtual environment
    if [[ "$OS" == "windows" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration"
    fi
    
    # Create necessary directories
    print_status "Creating directories..."
    mkdir -p uploads memvid_storage exports logs static data
    
    # Initialize database
    print_status "Initializing database..."
    python -c "from app.core.database import init_db; init_db()"
    
    cd ..
    print_success "Backend setup completed"
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration"
    fi
    
    cd ..
    print_success "Frontend setup completed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Create database directory
    mkdir -p database/data
    
    # Initialize database with schema
    if [ -f database/schemas/init.sql ]; then
        print_status "Initializing database schema..."
        sqlite3 database/data/bookbrain.db < database/schemas/init.sql
        print_success "Database schema initialized"
    fi
}

# Install Ollama (optional)
install_ollama() {
    print_status "Would you like to install Ollama for local AI models? (y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_status "Installing Ollama..."
        
        if [[ "$OS" == "linux" ]]; then
            curl -fsSL https://ollama.ai/install.sh | sh
        elif [[ "$OS" == "macos" ]]; then
            if command -v brew &> /dev/null; then
                brew install ollama
            else
                print_warning "Homebrew not found. Please install Ollama manually from https://ollama.ai"
            fi
        elif [[ "$OS" == "windows" ]]; then
            print_warning "Please install Ollama manually from https://ollama.ai"
        fi
        
        print_success "Ollama installation completed"
        print_status "To download a model, run: ollama pull llama2"
    fi
}

# Create startup scripts
create_scripts() {
    print_status "Creating startup scripts..."
    
    # Development startup script
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting BookBrain in development mode..."

# Start backend
echo "Starting backend..."
cd backend
source venv/bin/activate 2>/dev/null || source venv/Scripts/activate
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000 &
BACKEND_PID=$!
cd ..

# Start frontend
echo "Starting frontend..."
cd frontend
npm start &
FRONTEND_PID=$!
cd ..

echo "✅ BookBrain is starting..."
echo "📖 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

    chmod +x start-dev.sh
    
    # Production startup script
    cat > start-prod.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting BookBrain in production mode..."

# Build frontend
echo "Building frontend..."
cd frontend
npm run build
cd ..

# Start with Docker Compose
echo "Starting services with Docker Compose..."
docker-compose up -d

echo "✅ BookBrain is running in production mode"
echo "🌐 Application: http://localhost"
echo "🔧 API: http://localhost/api"
EOF

    chmod +x start-prod.sh
    
    print_success "Startup scripts created"
}

# Main setup function
main() {
    echo ""
    print_status "Starting BookBrain setup..."
    echo ""
    
    check_os
    check_requirements
    setup_backend
    setup_frontend
    setup_database
    install_ollama
    create_scripts
    
    echo ""
    print_success "🎉 BookBrain setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Update configuration files (.env) in backend/ and frontend/"
    echo "2. Start development server: ./start-dev.sh"
    echo "3. Open http://localhost:3000 in your browser"
    echo ""
    echo "For production deployment:"
    echo "1. Run: ./start-prod.sh"
    echo "2. Open http://localhost in your browser"
    echo ""
    echo "Documentation: https://github.com/HectorTa1989/BookBrain"
    echo "Issues: https://github.com/HectorTa1989/BookBrain/issues"
    echo ""
}

# Run main function
main "$@"
