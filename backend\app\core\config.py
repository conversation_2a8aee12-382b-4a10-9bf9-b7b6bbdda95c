"""
BookBrain Configuration Settings
Centralized configuration management using Pydantic Settings
"""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    APP_NAME: str = "BookBrain"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Personal Library Assistant"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Server settings
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",  # React dev server
        "http://127.0.0.1:3000",
        "http://localhost:8080",  # Alternative frontend port
        "http://127.0.0.1:8080",
    ]
    
    # Database settings
    DATABASE_URL: str = "sqlite:///./bookbrain.db"
    DATABASE_ECHO: bool = False  # Set to True for SQL query logging
    
    # File storage settings
    BASE_DIR: Path = Path(__file__).parent.parent.parent
    UPLOAD_DIR: str = str(BASE_DIR / "uploads")
    MEMVID_STORAGE_DIR: str = str(BASE_DIR / "memvid_storage")
    EXPORT_DIR: str = str(BASE_DIR / "exports")
    STATIC_DIR: str = str(BASE_DIR / "static")
    
    # File upload limits
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_FILE_TYPES: List[str] = [".pdf", ".txt", ".md", ".docx"]
    
    # MemVid settings
    MEMVID_CHUNK_SIZE: int = 512
    MEMVID_OVERLAP: int = 50
    MEMVID_FPS: int = 30
    MEMVID_FRAME_SIZE: int = 256
    MEMVID_VIDEO_CODEC: str = "h264"
    MEMVID_CRF: int = 23  # Quality setting (lower = better quality)
    
    # AI and ML settings
    EMBEDDING_MODEL: str = "all-MiniLM-L6-v2"
    EMBEDDING_DIMENSION: int = 384
    MAX_SEARCH_RESULTS: int = 20
    SIMILARITY_THRESHOLD: float = 0.7
    
    # AI API settings
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama2"
    HUGGINGFACE_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None  # Optional for premium features
    
    # Search settings
    SEARCH_INDEX_UPDATE_INTERVAL: int = 300  # 5 minutes
    VECTOR_STORE_TYPE: str = "faiss"  # Options: faiss, chromadb
    
    # Background task settings
    REDIS_URL: str = "redis://localhost:6379/0"
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = str(BASE_DIR / "logs" / "bookbrain.log")
    LOG_ROTATION: str = "10 MB"
    LOG_RETENTION: str = "30 days"
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # Performance settings
    WORKER_PROCESSES: int = 1
    MAX_CONCURRENT_UPLOADS: int = 5
    CACHE_TTL: int = 3600  # 1 hour
    
    # Export settings
    EXPORT_FORMATS: List[str] = ["pdf", "markdown", "json", "txt"]
    MAX_EXPORT_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("BASE_DIR", pre=True)
    def ensure_base_dir(cls, v):
        """Ensure base directory exists."""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Create global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance."""
    return settings


# Create necessary directories on import
for directory in [
    settings.UPLOAD_DIR,
    settings.MEMVID_STORAGE_DIR,
    settings.EXPORT_DIR,
    settings.STATIC_DIR,
    str(Path(settings.LOG_FILE).parent),
]:
    os.makedirs(directory, exist_ok=True)
