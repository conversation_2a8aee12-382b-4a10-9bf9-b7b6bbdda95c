"""
BookBrain Database Configuration and Connection Management
SQLAlchemy database setup with async support
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from loguru import logger

from core.config import settings
from models.database import Base


# Sync database engine and session
sync_engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

# Async database engine and session (for future async operations)
async_database_url = settings.DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///")
async_engine = create_async_engine(
    async_database_url,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
)

AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


def get_db() -> Session:
    """
    Dependency to get database session for sync operations.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get async database session.
    
    Yields:
        AsyncSession: SQLAlchemy async database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


def create_tables():
    """Create all database tables."""
    try:
        Base.metadata.create_all(bind=sync_engine)
        logger.info("✅ Database tables created successfully")
    except Exception as e:
        logger.error(f"❌ Failed to create database tables: {e}")
        raise


async def create_tables_async():
    """Create all database tables asynchronously."""
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("✅ Database tables created successfully (async)")
    except Exception as e:
        logger.error(f"❌ Failed to create database tables (async): {e}")
        raise


def drop_tables():
    """Drop all database tables."""
    try:
        Base.metadata.drop_all(bind=sync_engine)
        logger.info("🗑️ Database tables dropped successfully")
    except Exception as e:
        logger.error(f"❌ Failed to drop database tables: {e}")
        raise


async def drop_tables_async():
    """Drop all database tables asynchronously."""
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        logger.info("🗑️ Database tables dropped successfully (async)")
    except Exception as e:
        logger.error(f"❌ Failed to drop database tables (async): {e}")
        raise


def init_db():
    """Initialize the database with tables and initial data."""
    logger.info("🔧 Initializing database...")
    
    # Create tables
    create_tables()
    
    # Add initial data if needed
    db = SessionLocal()
    try:
        # Check if we need to add any initial data
        from models.database import Document
        
        # Example: Add sample data for development
        if settings.DEBUG and db.query(Document).count() == 0:
            logger.info("📝 Adding sample data for development...")
            # Add sample documents or configuration here if needed
            
        db.commit()
        logger.info("✅ Database initialization completed")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize database: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def init_db_async():
    """Initialize the database asynchronously."""
    logger.info("🔧 Initializing database (async)...")
    
    # Create tables
    await create_tables_async()
    
    # Add initial data if needed
    async with AsyncSessionLocal() as db:
        try:
            # Check if we need to add any initial data
            from models.database import Document
            from sqlalchemy import select
            
            # Example: Add sample data for development
            if settings.DEBUG:
                result = await db.execute(select(Document))
                if not result.first():
                    logger.info("📝 Adding sample data for development...")
                    # Add sample documents or configuration here if needed
                    
            await db.commit()
            logger.info("✅ Database initialization completed (async)")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database (async): {e}")
            await db.rollback()
            raise


def check_db_connection() -> bool:
    """
    Check if database connection is working.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        db = SessionLocal()
        # Try to execute a simple query
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


async def check_db_connection_async() -> bool:
    """
    Check if async database connection is working.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        async with AsyncSessionLocal() as db:
            await db.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Async database connection check failed: {e}")
        return False


def get_db_info() -> dict:
    """
    Get database information and statistics.
    
    Returns:
        dict: Database information
    """
    try:
        db = SessionLocal()
        
        # Get table information
        from models.database import Document, DocumentChunk, Note, SearchHistory, ChatSession, ChatMessage, ExportJob
        
        info = {
            "database_url": settings.DATABASE_URL,
            "connection_status": "connected",
            "tables": {
                "documents": db.query(Document).count(),
                "document_chunks": db.query(DocumentChunk).count(),
                "notes": db.query(Note).count(),
                "search_history": db.query(SearchHistory).count(),
                "chat_sessions": db.query(ChatSession).count(),
                "chat_messages": db.query(ChatMessage).count(),
                "export_jobs": db.query(ExportJob).count(),
            }
        }
        
        db.close()
        return info
        
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        return {
            "database_url": settings.DATABASE_URL,
            "connection_status": "error",
            "error": str(e)
        }


# Database event handlers
def setup_database_events():
    """Setup database event handlers for logging and monitoring."""
    
    from sqlalchemy import event
    
    @event.listens_for(sync_engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """Set SQLite pragmas for better performance."""
        if "sqlite" in settings.DATABASE_URL:
            cursor = dbapi_connection.cursor()
            # Enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys=ON")
            # Set journal mode to WAL for better concurrency
            cursor.execute("PRAGMA journal_mode=WAL")
            # Set synchronous mode to NORMAL for better performance
            cursor.execute("PRAGMA synchronous=NORMAL")
            # Set cache size (negative value means KB)
            cursor.execute("PRAGMA cache_size=-64000")  # 64MB cache
            cursor.close()
    
    @event.listens_for(sync_engine, "before_cursor_execute")
    def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        """Log slow queries in debug mode."""
        if settings.DEBUG and settings.DATABASE_ECHO:
            context._query_start_time = asyncio.get_event_loop().time()
    
    @event.listens_for(sync_engine, "after_cursor_execute")
    def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        """Log query execution time in debug mode."""
        if settings.DEBUG and settings.DATABASE_ECHO and hasattr(context, '_query_start_time'):
            total = asyncio.get_event_loop().time() - context._query_start_time
            if total > 0.1:  # Log queries taking more than 100ms
                logger.warning(f"Slow query ({total:.3f}s): {statement[:100]}...")


# Initialize database events
setup_database_events()
