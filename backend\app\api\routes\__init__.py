"""
BookBrain API Routes
Main router configuration for all API endpoints
"""

from fastapi import APIRouter

from .documents import router as documents_router
from .search import router as search_router
from .chat import router as chat_router
from .notes import router as notes_router
from .export import router as export_router
from .upload import router as upload_router

# Create main API router
api_router = APIRouter()

# Include all route modules
api_router.include_router(
    upload_router,
    prefix="/upload",
    tags=["upload"]
)

api_router.include_router(
    documents_router,
    prefix="/documents",
    tags=["documents"]
)

api_router.include_router(
    search_router,
    prefix="/search",
    tags=["search"]
)

api_router.include_router(
    chat_router,
    prefix="/chat",
    tags=["chat"]
)

api_router.include_router(
    notes_router,
    prefix="/notes",
    tags=["notes"]
)

api_router.include_router(
    export_router,
    prefix="/export",
    tags=["export"]
)
